import * as React from "react";

import { IconSvgProps } from "@/types";

export const Logo: React.FC<IconSvgProps> = ({
  size = 36,
  width,
  height,
  ...props
}) => (
 <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="19.5537109375" height="25.123291015625" viewBox="0 0 19.5537109375 25.123291015625"><g><g><path d="M16.5898065375,24.83153684375C18.4894285375,23.62640194375,19.8027753375,17.16273884375,19.5138483375,15.23802184375C19.5138483375,15.23802184375,19.2127962375,14.204675674438,18.4780588375,14.20458984375C17.7434072375,14.20458984375,17.4377222375,14.97865295375,17.4377222375,14.97865295375C17.4377222375,14.97865295375,17.244376137499998,15.69326404375,16.9408807375,16.59305384375C16.4714079375,17.98488614375,16.2739324375,19.70227434375,15.0495119375,20.13713834375C13.8250913375,20.57191654375,12.6005859375,20.16046524375,12.6005859375,20.16046524375C13.4331750875,23.31827544375,14.6901865375,26.03675884375,16.5898065375,24.83153684375Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M7.1781062625,13.0037412796875C6.4802541625,13.0819730796875,6.1126747625,13.345721279687499,5.9887170625,13.4697637796875C5.4988479625,13.9595308796875,5.2539634725,14.4493837796875,5.2539634725,14.6942681796875C5.2539634725,14.9391526796875,4.6777949325,13.5477418796875,4.5512266125,13.4173841796875C4.1743001925,13.0292577796875,4.1834793095,12.4090470796875,4.5716056824999995,12.0322045796875C5.1164479225,11.5031099296875,6.0074958625,11.1634893396875,6.9598331625,11.0567102436875C7.9476222624999995,10.9459733966875,9.118906962499999,11.0694265366875,10.3041696625,11.5943946796875C11.6874122625,12.2071103796875,12.4282960625,13.4990682796875,12.7081279625,14.6797837796875C12.8501911625,15.2791108796875,12.8864860625,15.8949422796875,12.8136434625,16.4562883796875C12.7429904625,17.0011319796875,12.5569696625,17.5838689796875,12.1700219625,18.0273198796875C11.364043262500001,18.9511937796875,10.1751594625,18.8471097796875,9.532043462499999,18.7106885796875C9.169601462500001,18.6338052796875,8.850022362499999,18.5208787796875,8.625180262499999,18.4301843796875C8.5111589625,18.3842057796875,8.4175176625,18.3422689796875,8.3502330625,18.310856779687498C8.3165483625,18.2950257796875,8.289264662499999,18.2818040796875,8.2690544625,18.2717847796875C8.2590331625,18.2667321796875,8.2506961625,18.2626046796875,8.2442121625,18.2593202796875L8.235875162500001,18.2550267796875L8.232759462499999,18.2534265796875L8.2309064625,18.252499579687502C8.2309064625,18.252499579687502,8.2304010625,18.2521619796875,8.6827802625,17.383278879687502L8.2309064625,18.252499579687502C7.7509908625,18.0026464796875,7.5640439624999996,17.4108152796875,7.8138961625,16.930899579687498C8.0632438625,16.4519948796875,8.6529703625,16.2652997796875,9.132296562499999,16.5129632796875M9.132296562499999,16.5129632796875L9.1393699625,16.5164994796875C9.147285462500001,16.5204572796875,9.161433262500001,16.527363779687498,9.1811380625,16.5365419796875C9.2206325625,16.5550679796875,9.2813491625,16.5823526796875,9.3579807625,16.6132573796875C9.5144433625,16.676331479687498,9.7210960625,16.7479943796875,9.9386119625,16.7941426796875C10.4553279625,16.9037847796875,10.6410961625,16.7996987796875,10.693812362500001,16.7393216796875C10.736253762499999,16.690646179687498,10.8287172625,16.5283727796875,10.8707380625,16.2043313796875C10.9106540625,15.8967942796875,10.8937272625,15.5197839796875,10.8017701625,15.131573679687499C10.6134748625,14.3370465796875,10.1626958625,13.6744794796875,9.5106544625,13.3857202796875C8.6606330625,13.0092153796875,7.8405904625,12.9294690796875,7.1781062625,13.0037412796875" fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M0.7009205775,2.8817758534375C0.4626553075,3.3674936234375,0.6632616515,3.9543991234374998,1.1489880074999999,4.1926641234375C1.8466470375,4.5348873234375,2.4520700375,4.8723364234375,3.0514972375,5.5482092234375C3.4104869375,5.952925723437501,4.029602737499999,5.9899778234375,4.4344027375,5.6309881234375005C4.8391189375,5.2719993234375,4.8761711375,4.652883523437501,4.5171816375,4.2481336234375C3.6630342375,3.2850770934374998,2.7837922375,2.8123779334375,2.0118177375,2.4337086634375C1.5260913375,2.1954431534375,0.9391858575,2.3960494994375,0.7009205775,2.8817758534375Z" fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M5.9961581200000005,0.029480457C5.47125721,0.16069746,5.15218353,0.69254589,5.283383369,1.2174134C5.37508869,1.5840993,5.46056223,1.8976407,5.53879404,2.1846638C5.68860483,2.7340279,5.81197309,3.1862555,5.90142107,3.7280235C5.98950529,4.2618175,6.49367385,4.623106,7.02748445,4.5349879C7.56121115,4.4468617,7.92255835,3.9427099,7.83439015,3.4089165C7.72870585,2.7685795,7.56407405,2.1627183,7.39885285,1.5546007C7.32575845,1.2853208,7.25241045,1.015594,7.1841158499999995,0.74223852C7.05291605,0.21737957,6.52104235,-0.10173631,5.9961581200000005,0.029480457Z" fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M11.5953331,0.632243874375C11.1327639,0.351595644375,10.5303221,0.499039649975,10.2496481,0.961565974375C9.73646927,1.807410234375,9.33209038,2.870837734375,9.1801748276,4.037111234375001C9.11036396,4.573599834375,9.48863792,5.065136434375,10.02505875,5.134997834375C10.5615644,5.204842134375,11.0531015,4.826576734375,11.1229954,4.290096734375C11.2380276,3.406307234375,11.5463219,2.601431334375,11.9246798,1.977886234375C12.2052698,1.515359834375,12.0578165,0.912892344375,11.5953331,0.632243874375Z" fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M13.750401,8.5719662125C15.693726,8.9602604125,15.693979,8.9588289125,15.693979,8.9588289125L15.694315,8.9573974125L15.69482,8.9543657125L15.696001,8.9482183125L15.698359,8.9349975125C15.699959,8.9258184125,15.701643,8.9157972125,15.703327,8.9049339125C15.706863,8.8833761125,15.710569,8.8585338125,15.71419,8.8307447125C15.721432,8.775503112500001,15.728758,8.7064504125,15.733643,8.626618412500001C15.74299,8.4725971125,15.744926,8.2489347125,15.705517,7.9894819125000005C15.630905,7.4979448125,15.344673,6.5663242324999995,14.335917,6.0262813525L14.136337,5.9194183325L13.918316,5.8590393025L13.416842,7.7278395125C13.864,7.9672499125,13.750401,8.5719662125,13.750401,8.5719662125ZM13.439663,9.8048925125L13.750401,8.5719662125L15.693726,8.9602604125L15.683031,9.0171871125L15.369347,10.2618189125C15.308968,10.5677557125,15.301052,10.742660512499999,15.304337,10.835039112499999C15.306948,10.910239212499999,15.316799,10.9349136125,15.3216,10.9470396125L15.322105,10.9484711125C15.3344,10.9797134125,15.366148,11.0475025125,15.482105,11.2233343125C15.529432,11.295082112500001,15.578526,11.3669128125,15.641178,11.4584503125C15.657684,11.4827022125,15.6752,11.5083027125,15.693895,11.535671212499999C15.778021,11.6589556125,15.876547,11.8042183125,15.980801,11.9668293125C16.632589,12.9825773125,16.69659,13.9574823125,16.693136,14.5822401125C16.693136,14.5913343125,16.693052,14.6005125125,16.692968,14.6096086125C16.770863,14.6363020125,16.853642,14.6632509125,16.941559,14.6912928125C16.970526,14.7004709125,17.00901,14.7125978125,17.049347,14.7253981125C17.106106,14.7432508125,17.166485,14.7623663125,17.209684,14.7761755125C17.294653,14.8034601125,17.403116,14.8389139125,17.508041,14.8779878125C17.581053,14.9051866125,17.651115,14.9323873125,17.716969,14.9596720125C17.780294,14.9858599125,17.857515,15.0194597125,17.93878,15.0604715125C18.001852,15.0923033125,18.104422,15.1468706125,18.220547,15.2273760125L19.241262,15.6666193125L19.226526,16.9742403125C19.217768,17.742997312500002,18.908463,18.553523312499998,18.689095,19.0553353125C18.447243,19.6085143125,18.127663,20.1984933125,17.833515,20.5906613125C17.422653,21.1382823125,16.957052,21.7165553125,16.397053,22.0864063125C16.066864,22.3043443125,15.668211,22.4776493125,15.197811,22.5344923125C14.778694,22.5851863125,14.416,22.5290183125,14.149557,22.4641763125L14.07099,22.4480933125C13.992085,22.4318393125,13.878906,22.4085123125,13.74122,22.3798823125C13.466189,22.3226203125,13.091285,22.2437133125,12.693727,22.1573143125C11.960673,21.9981533125,10.967073,21.7755013125,10.498358,21.6199653125L10.464505,21.6086813125L10.431074,21.596303312499998C10.223242,21.5190823125,9.871747,21.4325133125,9.2896004,21.299798312500002C9.2544003,21.2917123125,9.2185268,21.2835453125,9.1820631,21.2752923125C8.6798315,21.161019312500002,8.0617266,21.0203023125,7.4764633,20.8430393125C6.8703156,20.6594613125,6.1205053,20.3864503125,5.4839411,19.9415653125C4.8157301,19.4746183125,4.0560675,18.6512083125,4.0137091,17.4161763125C4.0073938,17.231249312499997,4.0116043,17.052556312500002,4.023057,16.8829543125C3.7221725,16.5568073125,3.4696252,16.130450312500002,3.342046,15.5845137125C3.2176673,15.0525560125,3.2864673,14.5809755125,3.3737094,14.2500296125C3.4611199,13.9181547125,3.5940039,13.6281347125,3.7050779,13.4112921125C3.7425516,13.3381128125,3.7814567,13.265944512499999,3.8201935,13.1962194125C3.4528673,13.0802603125,3.0380461,12.9371023125,2.6342566,12.764975512500001C2.1068881,12.540134412499999,1.3586187,12.1692715125,0.79999137,11.570618612499999C0.49912405,11.2481766125,0.18409252,10.7860288125,0.056715727,10.181818912499999C-0.07658124,9.5494814125,0.03418088,8.9428291125,0.29374313,8.423334112500001C0.9256084,7.1587448125,2.1325305,6.7120075225,3.2059619,6.6733551025C4.1753092,6.6384077025,5.2067194,6.9096498125,6.1308632,7.2337761125C6.9728842,7.5291023125,8.1781054,8.0784082125,9.2018528,8.5568075125C9.5391159,8.0038814125,9.9835787,7.3790398125,10.404379,6.8744497125L10.488,6.7741556124999995L10.584758,6.6861553225C11.3088,6.0271234525,12.122106,5.8055658345,12.741895,5.7579870225C13.045727,5.7347450255,13.307284,5.7520923615,13.502989,5.7775239945C13.601852,5.7903242115,13.687159,5.8056497575,13.756378,5.8201341625C13.791073,5.8273763655,13.822063,5.8345346455,13.849094,5.8411026005C13.862568,5.8443870545,13.875116,5.8475866325,13.886652,5.8506183625L13.903242,5.8549976325L13.91099,5.8570184725L13.914696,5.8581132925L13.916464,5.8586187325C13.916464,5.8586187325,13.918316,5.8590393025,13.416842,7.7278395125C13.416842,7.7278395125,12.634863,7.5113344125,11.96261,8.1047659125C11.955873,8.1107445125,11.949053,8.1168079125,11.942316,8.1229553125C11.921515,8.1479654125,11.900547,8.1733131125,11.879663,8.1989136125C11.8496,8.2357130125,11.819452,8.2731027125,11.789306,8.3110819125C11.780463,8.3221979125,11.771621,8.3333130125,11.762863,8.3445129125C11.091958,9.1957130125,11.131705,9.551334412500001,9.9071159,11.0208921125C9.9071159,11.0208921125,9.2692213,10.725482012499999,8.8876629,10.546113012500001C8.6258526,10.4229975125,8.3491373,10.2929764125,8.0671158,10.161860512499999C7.1573896,9.7388716125,6.1930103,9.3051023125,5.4989309,9.0616503125C3.7902148,8.4624081125,2.5304673,8.3862820125,2.0706441,9.3065348125C1.4640756,10.520513512499999,3.9272254,11.2238397125,5.1648674,11.577355412500001C5.425499,11.651797312500001,5.631815,11.7106609125,5.7437305,11.7555446125C6.3785267,12.0103655125,6.422821,12.5216083125,6.2337685,12.9798822125C6.1234531,13.247250512499999,6.0605478,13.3979864125,5.7439833,13.714533812500001C5.2541304,14.2043867125,5.1737938,14.7843447125,5.2673512,15.1844282125C5.3141727,15.3847666125,5.4068041,15.5304499125,5.5169516,15.6363878125C5.8123627,15.9205983125,6.2336845,15.9188303125,6.2336845,15.9188303125C6.2336845,15.9188303125,5.9620209,16.6129773125,5.9886312,17.388302312500002C5.994442,17.5589973125,6.0403366,17.7142823125,6.1194105,17.8563453125C6.5890532,18.6993763125,8.2287998,19.0729343125,9.6065683,19.3867023125C10.185179,19.518576312500002,10.717642,19.6398403125,11.097769,19.7808923125C11.779032,20.006997312499998,14.559832,20.5718813125,14.559832,20.5718813125C15.073011,20.7065343125,15.391157,20.5435873125,16.235115,19.4185343125C16.585684,18.9512513125,17.242611,17.629481312499998,17.25061,16.9150403125C17.144001,16.8692293125,17.136589,16.8532283125,17.129095,16.8371443125C17.118822,16.8150813125,17.108463,16.7928493125,16.838989,16.6924713125C16.748884,16.6588713125,16.62821,16.6208083125,16.490191,16.577271312500002C15.915621,16.3960503125,15.039242,16.1196723125,14.805052,15.6739454125C14.691284,15.4575243125,14.700294,15.1671667125,14.710737,14.8276281125C14.72699,14.2996282125,14.746948,13.6528082125,14.315285,12.980050112499999C14.209347,12.814913712500001,14.107706,12.6662817125,14.012632,12.5272503125C13.471327,11.735923812500001,13.142905,11.255755412500001,13.439663,9.8048925125ZM4.3881931,12.194703112500001C4.3894567,12.1910820125,4.3890352,12.1919241125,4.3874359,12.196807812500001L4.3881931,12.194703112500001ZM16.683115,15.2052297125C16.683115,15.2052297125,16.683115,15.1975660125,16.681601,15.1843443125C16.682106,15.1989126125,16.683115,15.2052297125,16.683115,15.2052297125Z" fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>
);

export const DiscordIcon: React.FC<IconSvgProps> = ({
  size = 24,
  width,
  height,
  ...props
}) => {
  return (
    <svg
      height={size || height}
      viewBox="0 0 24 24"
      width={size || width}
      {...props}
    >
      <path
        d="M14.82 4.26a10.14 10.14 0 0 0-.53 1.1 14.66 14.66 0 0 0-4.58 0 10.14 10.14 0 0 0-.53-1.1 16 16 0 0 0-4.13 1.3 17.33 17.33 0 0 0-3 11.59 16.6 16.6 0 0 0 5.07 2.59A12.89 12.89 0 0 0 8.23 18a9.65 9.65 0 0 1-1.71-.83 3.39 3.39 0 0 0 .42-.33 11.66 11.66 0 0 0 10.12 0q.21.18.42.33a10.84 10.84 0 0 1-1.71.84 12.41 12.41 0 0 0 1.08 1.78 16.44 16.44 0 0 0 5.06-2.59 17.22 17.22 0 0 0-3-11.59 16.09 16.09 0 0 0-4.09-1.35zM8.68 14.81a1.94 1.94 0 0 1-1.8-2 1.93 1.93 0 0 1 1.8-2 1.93 1.93 0 0 1 1.8 2 1.93 1.93 0 0 1-1.8 2zm6.64 0a1.94 1.94 0 0 1-1.8-2 1.93 1.93 0 0 1 1.8-2 1.92 1.92 0 0 1 1.8 2 1.92 1.92 0 0 1-1.8 2z"
        fill="currentColor"
      />
    </svg>
  );
};

export const TwitterIcon: React.FC<IconSvgProps> = ({
  size = 24,
  width,
  height,
  ...props
}) => {
  return (
    <svg
      height={size || height}
      viewBox="0 0 24 24"
      width={size || width}
      {...props}
    >
      <path
        d="M19.633 7.997c.013.175.013.349.013.523 0 5.325-4.053 11.461-11.46 11.461-2.282 0-4.402-.661-6.186-1.809.324.037.636.05.973.05a8.07 8.07 0 0 0 5.001-1.721 4.036 4.036 0 0 1-3.767-2.793c.249.037.499.062.761.062.361 0 .724-.05 1.061-.137a4.027 4.027 0 0 1-3.23-3.953v-.05c.537.299 1.16.486 1.82.511a4.022 4.022 0 0 1-1.796-3.354c0-.748.199-1.434.548-2.032a11.457 11.457 0 0 0 8.306 4.215c-.062-.3-.1-.611-.1-.923a4.026 4.026 0 0 1 4.028-4.028c1.16 0 2.207.486 2.943 1.272a7.957 7.957 0 0 0 2.556-.973 4.02 4.02 0 0 1-1.771 2.22 8.073 8.073 0 0 0 2.319-.624 8.645 8.645 0 0 1-2.019 2.083z"
        fill="currentColor"
      />
    </svg>
  );
};

export const GithubIcon: React.FC<IconSvgProps> = ({
  size = 24,
  width,
  height,
  ...props
}) => {
  return (
    <svg
      height={size || height}
      viewBox="0 0 24 24"
      width={size || width}
      {...props}
    >
      <path
        clipRule="evenodd"
        d="M12.026 2c-5.509 0-9.974 4.465-9.974 9.974 0 4.406 2.857 8.145 6.821 9.465.499.09.679-.217.679-.481 0-.237-.008-.865-.011-1.696-2.775.602-3.361-1.338-3.361-1.338-.452-1.152-1.107-1.459-1.107-1.459-.905-.619.069-.605.069-.605 1.002.07 1.527 1.028 1.527 1.028.89 1.524 2.336 1.084 2.902.829.091-.645.351-1.085.635-1.334-2.214-.251-4.542-1.107-4.542-4.93 0-1.087.389-1.979 1.024-2.675-.101-.253-.446-1.268.099-2.64 0 0 .837-.269 2.742 1.021a9.582 9.582 0 0 1 2.496-.336 9.554 9.554 0 0 1 2.496.336c1.906-1.291 2.742-1.021 2.742-1.021.545 1.372.203 2.387.099 2.64.64.696 1.024 1.587 1.024 2.675 0 3.833-2.33 4.675-4.552 4.922.355.308.675.916.675 1.846 0 1.334-.012 2.41-.012 2.737 0 .267.178.577.687.479C19.146 20.115 22 16.379 22 11.974 22 6.465 17.535 2 12.026 2z"
        fill="currentColor"
        fillRule="evenodd"
      />
    </svg>
  );
};

export const MoonFilledIcon = ({
  size = 24,
  width,
  height,
  ...props
}: IconSvgProps) => (
  <svg
    aria-hidden="true"
    focusable="false"
    height={size || height}
    role="presentation"
    viewBox="0 0 24 24"
    width={size || width}
    {...props}
  >
    <path
      d="M21.53 15.93c-.16-.27-.61-.69-1.73-.49a8.46 8.46 0 01-1.88.13 8.409 8.409 0 01-5.91-2.82 8.068 8.068 0 01-1.44-8.66c.44-1.01.13-1.54-.09-1.76s-.77-.55-1.83-.11a10.318 10.318 0 00-6.32 10.21 10.475 10.475 0 007.04 8.99 10 10 0 002.89.55c.16.01.32.02.48.02a10.5 10.5 0 008.47-4.27c.67-.93.49-1.519.32-1.79z"
      fill="currentColor"
    />
  </svg>
);

export const SunFilledIcon = ({
  size = 24,
  width,
  height,
  ...props
}: IconSvgProps) => (
  <svg
    aria-hidden="true"
    focusable="false"
    height={size || height}
    role="presentation"
    viewBox="0 0 24 24"
    width={size || width}
    {...props}
  >
    <g fill="currentColor">
      <path d="M19 12a7 7 0 11-7-7 7 7 0 017 7z" />
      <path d="M12 22.96a.969.969 0 01-1-.96v-.08a1 1 0 012 0 1.038 1.038 0 01-1 1.04zm7.14-2.82a1.024 1.024 0 01-.71-.29l-.13-.13a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.984.984 0 01-.7.29zm-14.28 0a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a1 1 0 01-.7.29zM22 13h-.08a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zM2.08 13H2a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zm16.93-7.01a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a.984.984 0 01-.7.29zm-14.02 0a1.024 1.024 0 01-.71-.29l-.13-.14a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.97.97 0 01-.7.3zM12 3.04a.969.969 0 01-1-.96V2a1 1 0 012 0 1.038 1.038 0 01-1 1.04z" />
    </g>
  </svg>
);

export const HeartFilledIcon = ({
  size = 24,
  width,
  height,
  ...props
}: IconSvgProps) => (
  <svg
    aria-hidden="true"
    focusable="false"
    height={size || height}
    role="presentation"
    viewBox="0 0 24 24"
    width={size || width}
    {...props}
  >
    <path
      d="M12.62 20.81c-.34.12-.9.12-1.24 0C8.48 19.82 2 15.69 2 8.69 2 5.6 4.49 3.1 7.56 3.1c1.82 0 3.43.88 4.44 2.24a5.53 5.53 0 0 1 4.44-2.24C19.51 3.1 22 5.6 22 8.69c0 7-6.48 11.13-9.38 12.12Z"
      fill="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
    />
  </svg>
);

export const SearchIcon = (props: IconSvgProps) => (
  <svg
    aria-hidden="true"
    fill="none"
    focusable="false"
    height="1em"
    role="presentation"
    viewBox="0 0 24 24"
    width="1em"
    {...props}
  >
    <path
      d="M11.5 21C16.7467 21 21 16.7467 21 11.5C21 6.25329 16.7467 2 11.5 2C6.25329 2 2 6.25329 2 11.5C2 16.7467 6.25329 21 11.5 21Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
    />
    <path
      d="M22 22L20 20"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
    />
  </svg>
);
